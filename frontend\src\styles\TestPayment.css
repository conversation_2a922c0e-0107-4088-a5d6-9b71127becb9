/* Enhanced Test Payment Page Styles */
.test-payment-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--bg-primary) 100%);
  font-family: var(--font-family-primary);
}

/* Enhanced Header */
.enhanced-header {
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
}

.brand-logo h1 {
  font-family: var(--font-family-display);
  font-size: var(--text-3xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.brand-star {
  font-size: var(--text-4xl);
  color: var(--accent-color);
  animation: twinkle 2s ease-in-out infinite alternate;
}

@keyframes twinkle {
  0% { opacity: 0.7; transform: scale(1); }
  100% { opacity: 1; transform: scale(1.1); }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.search-bar {
  position: relative;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-full);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  background: var(--gray-50);
}

.search-input:focus {
  border-color: var(--primary-color);
  background: var(--bg-primary);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
  outline: none;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.header-icons {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.icon-button {
  position: relative;
  padding: var(--space-2);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.icon-button:hover {
  background: var(--gray-100);
  color: var(--primary-color);
  transform: scale(1.05);
}

.cart-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--error-color);
  color: white;
  border-radius: var(--radius-full);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: 600;
  transform: translate(25%, -25%);
}

/* Page Title Section */
.page-title-section {
  text-align: center;
  padding: var(--space-12) var(--space-6) var(--space-8);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.page-title-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.page-title {
  font-family: var(--font-family-display);
  font-size: var(--text-5xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  position: relative;
  z-index: 1;
}

.page-subtitle {
  font-size: var(--text-xl);
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Enhanced Product Grid */
.products-section {
  padding: var(--space-12) var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.product-card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-slow);
  cursor: pointer;
  overflow: hidden;
  position: relative;
  transform-origin: center;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.02) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.product-card:hover {
  box-shadow: var(--shadow-2xl);
  transform: translateY(-8px) scale(1.02);
  border-color: var(--primary-color);
}

.product-card:hover::before {
  opacity: 1;
}

.product-image-container {
  position: relative;
  margin-bottom: var(--space-5);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 280px;
  object-fit: cover;
  transition: transform var(--transition-slow);
  border-radius: var(--radius-xl);
}

.product-card:hover .product-image {
  transform: scale(1.08);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.3) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-xl);
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.click-hint {
  background: rgba(255, 255, 255, 0.95);
  color: var(--text-primary);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: var(--text-sm);
  backdrop-filter: blur(10px);
  transform: translateY(10px);
  transition: transform var(--transition-normal);
}

.product-card:hover .click-hint {
  transform: translateY(0);
}

.product-info {
  position: relative;
  z-index: 2;
}

.product-brand {
  font-size: var(--text-sm);
  color: var(--text-muted);
  font-weight: 500;
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-name {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  line-height: 1.3;
  font-family: var(--font-family-display);
}

.product-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.price-current {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--error-color);
  font-family: var(--font-family-display);
}

.price-original {
  font-size: var(--text-lg);
  color: var(--text-muted);
  text-decoration: line-through;
}

.price-discount {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--gray-200);
}

.product-colors {
  display: flex;
  gap: var(--space-2);
}

.color-dot {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  border: 2px solid var(--gray-300);
  transition: transform var(--transition-fast);
}

.color-dot:hover {
  transform: scale(1.2);
}

.product-sku {
  font-size: var(--text-xs);
  color: var(--text-muted);
  background: var(--gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-weight: 500;
}

/* Instructions Section */
.instructions-section {
  background: var(--gray-50);
  padding: var(--space-12) var(--space-6);
  border-top: 1px solid var(--gray-200);
}

.instructions-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.instructions-title {
  font-family: var(--font-family-display);
  font-size: var(--text-3xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

.instructions-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.instruction-item {
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.instruction-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border-radius: var(--radius-full);
  font-weight: 700;
  margin-bottom: var(--space-3);
}

.instruction-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: var(--space-3) var(--space-4);
    flex-direction: column;
    gap: var(--space-4);
  }

  .search-bar {
    min-width: 250px;
  }

  .page-title {
    font-size: var(--text-4xl);
  }

  .page-subtitle {
    font-size: var(--text-lg);
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: var(--space-4);
  }

  .products-section {
    padding: var(--space-8) var(--space-4);
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: var(--space-3);
  }

  .search-bar {
    min-width: 200px;
  }

  .page-title-section {
    padding: var(--space-8) var(--space-4) var(--space-6);
  }

  .page-title {
    font-size: var(--text-3xl);
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .instruction-item {
    padding: var(--space-4);
  }
}

/* Loading Animation */
.loading-products {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--gray-300);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
