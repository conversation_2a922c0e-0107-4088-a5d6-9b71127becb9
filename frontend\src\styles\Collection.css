.collections-container {
  display: flex;
  flex-direction: column;
}

.product-card-horizontal {
  min-width: 350px; /* Increased from 300px */
  max-width: 350px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 0.75rem; /* Slightly more rounded */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
}

.collection-section {
  width: 90%;
  margin: auto;
  max-width: 1400px; /* Limit max width for better layout control */
}

.collection-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ebdcdc;
  background-color: #041b4b;
  text-align: center;
  width: 30%;
  margin: auto;
  border-radius: 20px;
}

/* Original grid layout (keeping for backward compatibility) */
.products-grid {
  display: flex;
  flex-direction: row;
  gap: 3rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scroll-snap-type: x mandatory;
  margin-top: 3rem;
}

/* New horizontal scrolling layout */
.products-horizontal-scroll {
  margin-top: 3rem;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 1rem;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #041b4b #f1f1f1;
}

.products-horizontal-scroll::-webkit-scrollbar {
  height: 8px;
}

.products-horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.products-horizontal-scroll::-webkit-scrollbar-thumb {
  background: #041b4b;
  border-radius: 10px;
}

.products-horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

.products-horizontal-grid {
  display: flex;
  gap: 2rem;
  padding: 0.5rem;
  min-width: max-content;
}

.product-card-horizontal {
  min-width: 300px;
  max-width: 300px;
  flex: 0 0 300px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
}

.product-card-horizontal:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}

.product-card {
  min-width: 300px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
}

.product-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}
.product-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}

.product-image-container {
  position: relative;
  overflow: hidden;
  border-top-left-radius: 0.75rem; /* Updated to match card radius */
  border-top-right-radius: 0.75rem;
  width: 100%;
  background-color: #041b4b;
}

.sale-badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background-color: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: bold;
  border-radius: 9999px;
  z-index: 10;
}

.product-image-container .product-image {
  width: 100%;
  height: 280px; /* Increased from 20rem (320px) to 280px for better proportion */
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image,
.product-card-horizontal:hover .product-image {
  transform: scale(1.05);
}

.product-info {
  padding: 1.75rem; /* Increased padding for better spacing */
}

.product-name {
  font-size: 1.25rem; /* Increased font size */
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem; /* Increased margin */
  transition: color 0.3s ease;
  line-height: 1.4; /* Better line height for readability */
}

.product-card:hover .product-name,
.product-card-horizontal:hover .product-name {
  color: #dc2626;
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-price {
  font-size: 1.375rem; /* Increased font size */
  font-weight: bold;
  color: #dc2626;
}

.original-price {
  font-size: 1rem; /* Increased font size */
  color: #6b7280;
  text-decoration: line-through;
}

/* Performance Optimization Styles */

/* Loading States */
.collections-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.loading-spinner-large {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #041b4b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #041b4b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Lazy Image Loading */
.lazy-image-container {
  position: relative;
  width: 100%;
  height: 280px;
  overflow: hidden;
  background-color: #f8fafc;
}

.product-image.loading {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-image.loaded {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Load More Button */
.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  padding: 1rem;
}

.load-more-btn {
  background: linear-gradient(135deg, #041b4b, #1e40af);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(4, 27, 75, 0.3);
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(4, 27, 75, 0.4);
  background: linear-gradient(135deg, #1e40af, #2563eb);
}

.load-more-btn:active {
  transform: translateY(0);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Improved Grid Layout for Better Performance */
.products-horizontal-grid {
  display: flex;
  gap: 2rem;
  padding: 0.5rem;
  min-width: max-content;
  will-change: transform; /* Optimize for animations */
}

/* Optimize card transitions */
.product-card-horizontal {
  will-change: transform, box-shadow;
  backface-visibility: hidden; /* Prevent flickering */
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Responsive optimizations */
@media (max-width: 768px) {
  .products-horizontal-grid {
    gap: 1rem;
    padding: 0.25rem;
  }

  .product-card-horizontal {
    min-width: 250px;
    max-width: 250px;
    flex: 0 0 250px;
  }

  .product-image {
    height: 200px;
  }

  .lazy-image-container {
    height: 200px;
  }

  .product-info {
    padding: 1rem;
  }

  .product-name {
    font-size: 1rem;
  }

  .current-price {
    font-size: 1.125rem;
  }

  .load-more-btn {
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .product-card-horizontal {
    min-width: 200px;
    max-width: 200px;
    flex: 0 0 200px;
  }

  .product-image {
    height: 160px;
  }

  .lazy-image-container {
    height: 160px;
  }
}