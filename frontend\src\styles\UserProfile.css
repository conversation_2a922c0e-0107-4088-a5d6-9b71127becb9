/* Frontend: UserProfile Component Styles */
/* File: frontend/src/components/UserProfile.css */

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.profile-header {
  text-align: center;
  margin-bottom: 30px;
}

.profile-header h1 {
  color: #2c3e50;
  font-size: 2.5em;
  margin-bottom: 10px;
}

.verification-badge {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  display: inline-block;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
  }
}

/* Profile Cards */
.profile-card, .stats-card, .recent-orders-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e9ea;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-card:hover, .stats-card:hover, .recent-orders-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.profile-card h2, .stats-card h2, .recent-orders-card h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.4em;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

/* Profile Information */
.profile-info .info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ecf0f1;
}

.profile-info .info-row:last-child {
  border-bottom: none;
}

.profile-info .label {
  font-weight: 600;
  color: #7f8c8d;
  font-size: 0.95em;
}

.profile-info .value {
  font-weight: 500;
  color: #2c3e50;
}

.profile-info .value.role {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.85em;
  text-transform: capitalize;
}

.profile-info .value.status.active {
  color: #27ae60;
  font-weight: 600;
}

.profile-info .value.status.inactive {
  color: #e74c3c;
  font-weight: 600;
}

/* Edit Form */
.edit-form .form-group {
  margin-bottom: 20px;
}

.edit-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.edit-form input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e8e9ea;
  border-radius: 8px;
  font-size: 1em;
  transition: border-color 0.3s ease;
}

.edit-form input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* Buttons */
.edit-btn, .save-btn, .cancel-btn, .retry-btn, .refresh-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.95em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.edit-btn {
  background: #3498db;
  color: white;
  width: 100%;
  margin-top: 15px;
}

.edit-btn:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.save-btn {
  background: #27ae60;
  color: white;
  flex: 1;
}

.save-btn:hover {
  background: #229954;
}

.cancel-btn {
  background: #e74c3c;
  color: white;
  flex: 1;
}

.cancel-btn:hover {
  background: #c0392b;
}

.retry-btn, .refresh-btn {
  background: #f39c12;
  color: white;
}

.retry-btn:hover, .refresh-btn:hover {
  background: #e67e22;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid #e8e9ea;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #3498db;
  color: white;
  transform: scale(1.05);
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 5px;
}

.stat-item:hover .stat-number {
  color: white;
}

.stat-label {
  font-size: 0.9em;
  color: #7f8c8d;
  font-weight: 500;
}

.stat-item:hover .stat-label {
  color: #ecf0f1;
}

/* Recent Orders */
.recent-orders-card {
  grid-column: 1 / -1;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #3498db;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.order-item:hover {
  background: #e8f4f8;
  transform: translateX(5px);
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1em;
}

.order-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 5px;
}

.order-total {
  font-weight: 600;
  color: #27ae60;
  font-size: 1.1em;
}

.order-date {
  font-size: 0.85em;
  color: #7f8c8d;
}

.order-status {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.status-badge, .payment-badge {
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8em;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #f39c12;
  color: white;
}

.status-badge.processing {
  background: #3498db;
  color: white;
}

.status-badge.completed {
  background: #27ae60;
  color: white;
}

.status-badge.cancelled {
  background: #e74c3c;
  color: white;
}

.payment-badge.paid {
  background: #27ae60;
  color: white;
}

.payment-badge.pending {
  background: #f39c12;
  color: white;
}

.payment-badge.failed {
  background: #e74c3c;
  color: white;
}

/* Database Verification */
.db-verification {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  margin-top: 20px;
}

.db-verification h3 {
  margin-bottom: 15px;
  font-size: 1.3em;
}

.db-verification p {
  margin: 10px 0;
  font-size: 1.05em;
}

/* Loading and Error States */
.loading {
  text-align: center;
  padding: 50px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 40px;
  background: #ffeaa7;
  border-radius: 15px;
  border-left: 5px solid #e17055;
}

.error-message h2 {
  color: #d63031;
  margin-bottom: 15px;
}

.error-message p {
  color: #2d3436;
  margin-bottom: 20px;
  font-size: 1.1em;
}
