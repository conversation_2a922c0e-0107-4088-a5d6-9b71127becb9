.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.register-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  border: 1px solid #e2e8f0;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.gender-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gender-label {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.gender-options {
  display: flex;
  gap: 16px;
}

.gender-btn {
  flex: 1;
  padding: 12px 24px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.gender-btn:hover {
  border-color: #9ca3af;
}

.gender-btn.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #3b82f6;
}

.name-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.location-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  width: 100%;
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  color: #6b7280;
  background-color: #f9fafb;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: #9ca3af;
  font-size: 16px;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: #ffffff;
  color: #374151;
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.create-account-button {
  width: 100%;
  background-color: #000000;
  color: white;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 8px;
}

.create-account-button:hover {
  background-color: #1f2937;
}

.create-account-button:active {
  transform: translateY(1px);
}

.divider {
  text-align: center;
  position: relative;
  margin: 16px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e5e7eb;
}

.divider span {
  background-color: white;
  color: #6b7280;
  padding: 0 16px;
  font-size: 16px;
  font-weight: 500;
}

.facebook-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  color: #374151;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.facebook-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.facebook-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  border-radius: 50%;
  background-color: #1877f2;
  color: white;
}

.login-section {
  text-align: center;
  margin-top: 8px;
}

.login-text {
  color: #374151;
  font-size: 16px;
  font-weight: 500;
}

.login-link {
  color: #374151;
  text-decoration: underline;
  font-weight: 600;
  transition: color 0.2s ease;
}

.login-link:hover {
  color: #1f2937;
}

/* Responsive Design */
@media (max-width: 640px) {
  .register-container {
    padding: 16px;
  }
  
  .register-card {
    padding: 24px;
  }
  
  .name-row,
  .location-row {
    grid-template-columns: 1fr;
  }
  
  .gender-options {
    flex-direction: column;
  }
  
  .form-input,
  .form-select {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Focus styles for better accessibility */
.facebook-button:focus,
.create-account-button:focus,
.form-input:focus,
.form-select:focus,
.gender-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
