const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const path = require("path");
require("dotenv").config();

// Import database configuration
const { connectDB } = require("./config/database");
// Import models to ensure associations are set up
require("./models");

// Import routes
const authRoutes = require("./routes/auth");
const productRoutes = require("./routes/products");
const categoryRoutes = require("./routes/categories");
const orderRoutes = require("./routes/orders");
const userRoutes = require("./routes/users");
const cartRoutes = require("./routes/cart");
const paymentRoutes = require("./routes/payments");
const testRoutes = require("./routes/test");
const debugRoutes = require("./routes/debug");
const userProfileRoutes = require("./routes/userProfile");
const orderVerificationRoutes = require("./routes/orderVerification");

const app = express();

// Basic middleware
app.use(helmet());
app.use(morgan("combined"));

// CORS configuration
const corsOptions = {
  origin: [
    process.env.CORS_ORIGIN || "http://localhost:5174",
    "http://localhost:3000",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5174",
    "http://localhost:3001"
  ],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "Accept",
    "Origin",
    "X-Requested-With",
    "Cache-Control",
    "X-Access-Token",
  ],
  exposedHeaders: ["Authorization"],
  optionsSuccessStatus: 200,
  preflightContinue: false,
};

app.use(cors(corsOptions));
app.options("*", cors(corsOptions));

// Request parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Static files
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Request logging
app.use((req, res, next) => {
  console.log(`🔄 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/products", productRoutes);
app.use("/api/categories", categoryRoutes);
app.use("/api/orders", orderRoutes);
app.use("/api/users", userRoutes);
app.use("/api/cart", cartRoutes);
app.use("/api/payments", paymentRoutes);
app.use("/api/test", testRoutes);
app.use("/api/user", userProfileRoutes);
app.use("/api/verification", orderVerificationRoutes);

// Debug routes (development only)
if (process.env.NODE_ENV === "development") {
  app.use("/api/debug", debugRoutes);
}

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    status: "OK",
    message: "StyleStore API is running",
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 3003,
    environment: process.env.NODE_ENV
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('💥 Server Error:', err.stack);
  res.status(500).json({
    message: "Something went wrong!",
    error: process.env.NODE_ENV === "development" ? err.message : "Internal server error",
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({ 
    message: "Route not found",
    path: req.originalUrl,
    method: req.method
  });
});

// Graceful startup
const startServer = async () => {
  try {
    console.log('🚀 Starting StyleStore Backend Server...');
    console.log('📋 Environment:', process.env.NODE_ENV);
    
    // Connect to database first
    await connectDB();
    
    const PORT = process.env.PORT || 3003;
    
    // Start server with error handling
    const server = app.listen(PORT, (err) => {
      if (err) {
        console.error('❌ Failed to start server:', err);
        process.exit(1);
      }
      
      console.log('🎉 ===================================');
      console.log(`🎉 StyleStore Server Started Successfully!`);
      console.log(`🎉 ===================================`);
      console.log(`🌐 Server URL: http://localhost:${PORT}`);
      console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
      console.log(`📚 API Base URL: http://localhost:${PORT}/api`);
      console.log(`🔗 Frontend URL: ${process.env.CORS_ORIGIN || "http://localhost:5174"}`);
      console.log(`📊 Database: ${process.env.DB_NAME}@${process.env.DB_HOST}`);
      console.log('🎉 ===================================');
    });

    // Handle server errors
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use!`);
        console.log('💡 Try these solutions:');
        console.log('   1. Kill the process using the port');
        console.log('   2. Change PORT in .env file');
        console.log('   3. Run: npm run restart');
        process.exit(1);
      } else {
        console.error('❌ Server error:', error);
        process.exit(1);
      }
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down server gracefully...');
      server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('💥 Failed to start server:', error.message);
    process.exit(1);
  }
};

// Start the server
startServer();
