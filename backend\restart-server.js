#!/usr/bin/env node

/**
 * Server restart utility for development
 * This script helps restart the server with proper cleanup
 */

const { exec } = require('child_process');
const path = require('path');

console.log('🔄 Restarting StyleStore Backend Server...');

// Kill any existing processes on port 3003
const killPort = (port) => {
  return new Promise((resolve) => {
    exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
      if (stdout) {
        const lines = stdout.split('\n');
        const pids = [];
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 5 && parts[1].includes(`:${port}`)) {
            const pid = parts[4];
            if (pid && pid !== '0') {
              pids.push(pid);
            }
          }
        });
        
        if (pids.length > 0) {
          console.log(`⚠️ Found processes on port ${port}: ${pids.join(', ')}`);
          pids.forEach(pid => {
            exec(`taskkill /PID ${pid} /F`, (killError) => {
              if (killError) {
                console.log(`Failed to kill PID ${pid}: ${killError.message}`);
              } else {
                console.log(`✅ Killed process ${pid}`);
              }
            });
          });
          
          setTimeout(resolve, 2000); // Wait for processes to be killed
        } else {
          resolve();
        }
      } else {
        resolve();
      }
    });
  });
};

const startServer = async () => {
  try {
    // Kill existing processes
    await killPort(3003);
    
    console.log('🚀 Starting server on port 3003...');
    
    // Start the server
    const serverProcess = exec('npm run dev', {
      cwd: __dirname,
      stdio: 'inherit'
    });
    
    serverProcess.stdout.on('data', (data) => {
      console.log(data.toString());
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(data.toString());
    });
    
    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
  }
};

startServer();
