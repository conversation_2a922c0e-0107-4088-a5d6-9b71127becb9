/* ProductDetail.css */
.product-detail-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  flex-direction: column;
}

.loading {
  font-size: 1.2rem;
  color: #666;
}

.error {
  font-size: 1.2rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.go-back-btn {
  padding: 0.5rem 1rem;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.go-back-btn:hover {
  background-color: #333;
}

/* Header styles (reuse from existing components) */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  border-bottom: 1px solid #e5e5e5;
}

.logo-container .logo-bg {
  text-decoration: none;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #000;
}

.logo-star {
  color: #ff6b6b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.icons-area {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.shopping-bag-rel {
  position: relative;
}

.shopping-bag-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.login-link, .register-link {
  text-decoration: none;
  color: #000;
  font-weight: 500;
}

.login-link:hover, .register-link:hover {
  color: #ff6b6b;
}

/* Category navigation */
.category-nav {
  display: flex;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 1rem 0;
  border-bottom: 1px solid #e5e5e5;
}

.category-group {
  position: relative;
  margin: 0 1rem;
}

.category-btn {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 1rem;
  color: #333;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.category-btn:hover {
  background-color: #e9ecef;
  color: #ff6b6b;
}

.category-btn.women {
  color: #ff6b6b;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.category-group.open .dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown li {
  border-bottom: 1px solid #f0f0f0;
}

.dropdown li:last-child {
  border-bottom: none;
}

.dropdown a {
  display: block;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown a:hover {
  background-color: #f8f9fa;
  color: #ff6b6b;
}

/* Product detail container */
.product-detail-container {
  flex: 1;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
  
}

.product-detail-content {
  display: flex;
  gap: 3rem;
  align-items: flex-start;
}

/* Product images section */
.product-images-section {
  flex: 1;
  display: flex;
  gap: 1rem;
}

.image-thumbnails {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.thumbnail:hover {
  border-color: #ddd;
}

.thumbnail.active {
  border-color: #ff6b6b;
}

.main-image-container {
  position: relative;
  flex: 1;
}

.favorite-btn-detail {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.3s ease;
}

.favorite-btn-detail:hover {
  transform: scale(1.1);
}

.favorite-btn-detail.active {
  background-color: #ffe5e5;
}

.main-product-image {
  width: 100%;
  max-width: 400px;
  height: auto;
  object-fit: cover;
  border-radius: 12px;
}

/* Product info section */
.product-info-section {
  flex: 1;
  max-width: 400px;
}

.product-details {
  margin-bottom: 2rem;
}

.product-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.product-code {
  color: #666;
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
}

.size-quantity-container {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.size-selection, .quantity-selection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.size-label, .quantity-label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.size-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-width: 80px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quantity-btn:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  min-width: 40px;
  text-align: center;
  font-weight: 500;
}

.price-section {
  margin-bottom: 2rem;
}

.price-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.original-price-large {
  font-size: 1rem;
  color: #999;
  text-decoration: line-through;
}

.discount-percentage {
  color: #ef4444;
  font-weight: 500;
  font-size: 0.9rem;
}

.sale-price-large {
  font-size: 1.2rem;
  font-weight: bold;
  color: #ef4444;
}

/* Order summary */
.order-summary {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  color: #666;
  font-size: 0.95rem;
}

.summary-value {
  font-weight: 500;
  color: #333;
}

.save-amount {
  color: #28a745;
}

.summary-divider {
  border: none;
  border-top: 1px solid #dee2e6;
  margin: 1rem 0;
}

.total-row {
  font-size: 1.1rem;
  font-weight: bold;
  margin-top: 1rem;
}

.checkout-btn {
  width: 100%;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-top: 1.5rem;
  transition: background-color 0.3s ease;
}

.checkout-btn:hover {
  background-color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .product-detail-content {
    flex-direction: column;
    gap: 2rem;
  }
  
  .product-images-section {
    flex-direction: column-reverse;
  }
  
  .image-thumbnails {
    flex-direction: row;
    justify-content: center;
    overflow-x: auto;
  }
  
  .size-quantity-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .header {
    padding: 1rem;
  }
  
  .header-row {
    flex-wrap: wrap;
  }
  
  .search-input {
    min-width: 150px;
  }
  
  .category-nav {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .category-group {
    margin: 0.25rem;
  }
}

@media (max-width: 480px) {
  .product-detail-container {
    padding: 0 1rem;
  }
  
  .main-product-image {
    max-width: 100%;
  }
  
  .order-summary {
    padding: 1rem;
  }
  
  .icons-area {
    gap: 0.5rem;
  }
  
  .login-link, .register-link {
    font-size: 0.9rem;
  }
}
