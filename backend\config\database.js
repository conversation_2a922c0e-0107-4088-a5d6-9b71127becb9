const { Sequelize } = require('sequelize');

// Enhanced Sequelize configuration for cloud MySQL (Aiven)
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false
      },
      connectTimeout: 120000, // 2 minutes
      flags: '-FOUND_ROWS'
    },
    logging: process.env.NODE_ENV === 'development' ? 
      (sql) => console.log('🗄️ SQL:', sql.substring(0, 100) + '...') : false,
    pool: {
      max: 5,        // Reduced for cloud DB
      min: 1,        // Keep at least 1 connection
      acquire: 120000, // 2 minutes
      idle: 60000,   // 1 minute
      evict: 5000,   // Check every 5 seconds
      handleDisconnects: true
    },
    retry: {
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /TIMEOUT/,
        /Can't add new command when connection is in closed state/,
        /Connection lost/,
        /ER_CON_COUNT_ERROR/,
        /PROTOCOL_CONNECTION_LOST/,
        /PROTOCOL_ENQUEUE_AFTER_QUIT/,
        /PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR/,
        /PROTOCOL_ENQUEUE_HANDSHAKE_TWICE/,
        /Connection is in closed state/
      ],
      max: 3 // Reduced retry attempts
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_general_ci',
      timestamps: true,
      underscored: false
    },
    // Additional options for stability
    benchmark: false,
    isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    transactionType: Sequelize.Transaction.TYPES.DEFERRED
  }
);

// Simplified connection event handlers
sequelize.addHook('afterConnect', () => {
  console.log('✅ Database connection established');
});

sequelize.addHook('beforeDisconnect', () => {
  console.log('⚠️ Database connection closing...');
});

// Graceful connection management
const connectDB = async () => {
  let retryCount = 0;
  const maxRetries = 3;
  
  while (retryCount < maxRetries) {
    try {
      console.log(`🔄 Attempting database connection (attempt ${retryCount + 1}/${maxRetries})...`);
      
      await sequelize.authenticate();
      console.log(`✅ MySQL Connected: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
      console.log(`📊 Database: ${process.env.DB_NAME}`);
      
      // Only sync tables in development with safe mode
      if (process.env.NODE_ENV === 'development' && !process.env.SEEDING_MODE) {
        const syncMode = process.env.DB_SYNC_MODE || 'safe';
        console.log(`🔧 Database sync mode: ${syncMode}`);
        
        if (syncMode === 'force') {
          await sequelize.sync({ force: true });
          console.log('⚠️ Database tables recreated (force mode)');
        } else if (syncMode === 'alter') {
          await sequelize.sync({ alter: true });
          console.log('🔧 Database tables altered');
        } else {
          await sequelize.sync({ force: false, alter: false });
          console.log('✅ Database tables synchronized (safe mode)');
        }
      } else if (process.env.SEEDING_MODE) {
        console.log('⏭️ Skipping auto-sync (seeding mode active)');
      }
      
      return; // Success - exit retry loop
      
    } catch (error) {
      retryCount++;
      console.error(`❌ Database connection attempt ${retryCount} failed:`, error.message);
      
      if (retryCount >= maxRetries) {
        console.error('💥 All database connection attempts failed');
        console.error('💡 Please check your database credentials and connection');
        process.exit(1);
      }
      
      // Wait before retrying (exponential backoff)
      const waitTime = Math.pow(2, retryCount) * 1000;
      console.log(`⏳ Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};

module.exports = { sequelize, connectDB };
