/* Modern App Styles */
#root {
  max-width: 1400px;
  margin: 0 auto;
  text-align: left;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Enhanced Logo Styles */
.logo {
  height: 4rem;
  padding: var(--space-4);
  will-change: filter;
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
}

.logo:hover {
  filter: drop-shadow(0 0 1rem var(--primary-color));
  transform: scale(1.05);
}

.logo.react:hover {
  filter: drop-shadow(0 0 1rem #61dafb);
}

/* Smooth Logo Animation */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

/* Enhanced Card Component */
.card {
  padding: var(--space-8);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

/* Product Grid Layouts */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-6);
  padding: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
}

.product-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  cursor: pointer;
  overflow: hidden;
}

.product-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
  border-color: var(--primary-color);
}

.product-card img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  transition: transform var(--transition-slow);
}

.product-card:hover img {
  transform: scale(1.05);
}

.product-info {
  padding: var(--space-4) 0;
}

.product-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  font-family: var(--font-family-display);
}

.product-price {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

.price-current {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--error-color);
  font-family: var(--font-family-display);
}

.price-original {
  font-size: var(--text-base);
  color: var(--text-muted);
  text-decoration: line-through;
}

.price-discount {
  font-size: var(--text-sm);
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-weight: 600;
}

/* Navigation Styles */
.main-nav {
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-links {
  display: flex;
  gap: var(--space-6);
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  transition: color var(--transition-fast);
  text-decoration: none;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

/* Search Styles */
.search-container {
  position: relative;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-full);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 3px solid var(--gray-300);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error States */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(239, 68, 68, 0.2);
  font-weight: 500;
}

/* Success States */
.success-message {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(16, 185, 129, 0.2);
  font-weight: 500;
}

/* Documentation Text */
.read-the-docs {
  color: var(--text-muted);
  font-size: var(--text-sm);
  text-align: center;
  margin-top: var(--space-8);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--space-4);
    padding: var(--space-4);
  }

  .nav-container {
    padding: 0 var(--space-3);
    height: 60px;
  }

  .nav-links {
    gap: var(--space-4);
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
    padding: var(--space-3);
  }

  .nav-links {
    flex-direction: column;
    gap: var(--space-2);
  }
}
