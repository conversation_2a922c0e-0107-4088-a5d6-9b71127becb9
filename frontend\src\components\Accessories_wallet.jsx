import React, { useState } from "react";
import { Heart, Bell, ShoppingBag } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import "../styles/Women_T_shirt.css";
import Footer from "../components/Footer";

import accessories_wallet1 from "../assets/wallet1.webp";
import accessories_wallet2 from "../assets/wallet2.webp";
import accessories_wallet3 from "../assets/wallet3.jpg";
import accessories_wallet4 from "../assets/wallet4.webp";
import accessories_wallet5 from "../assets/wallet5.webp";
import accessories_wallet6 from "../assets/wallet6.webp";
import accessories_wallet7 from "../assets/wallet7.webp";
import accessories_wallet8 from "../assets/wallet.jpg";

const categories = [
  {
    name: "Women",
    items: [
      { name: "T-shirt", link: "/women/t-shirt" },
      { name: "Shirt", link: "/women/shirt" },
      { name: "Jacket", link: "/women/jacket" },
      { name: "Skirt", link: "/women/skirt" },
      { name: "Short<PERSON>", link: "/women/shorts" },
      { name: "Jean<PERSON>", link: "/women/jeans" },
      { name: "Trouser", link: "/women/trouser" },
      { name: "Dress", link: "/women/dress" },
      { name: "Shoes", link: "/women/shoes" },
    ],
  },
  {
    name: "Men",
    items: [
      { name: "T-shirt", link: "/men/t-shirt" },
      { name: "Jeans", link: "/men/jeans" },
      { name: "Jacket", link: "/men/jacket" },
      { name: "Trouser", link: "/men/trouser" },
      { name: "Shirt", link: "/men/shirt" },
      { name: "Shoes", link: "/men/shoes" },
    ],
  },
  {
    name: "Girls",
    items: [
      { name: "Clothing", link: "/girls/clothing" },
      { name: "Shoes", link: "/girls/shoes" },
    ],
  },
  {
    name: "Boys",
    items: [
      { name: "Clothing", link: "/boys/clothing" },
      { name: "Shoes", link: "/boys/shoes" },
    ],
  },
  {
    name: "Accessories",
    items: [
      { name: "Glasses", link: "/accessories/glasses" },
      { name: "Watches", link: "/accessories/watches" },
      { name: "Gloves", link: "/accessories/gloves" },
      { name: "Belt", link: "/accessories/belt" },
      { name: "Hat", link: "/accessories/hat" },
      { name: "Bag", link: "/accessories/bag" },
      { name: "Wallet", link: "/accessories/wallet" },
    ],
  },
];

const products = [
  {
    id: 1,
    name: "Leather Bifold Wallet",
    originalPrice: 49.99,
    salePrice: 9.99,
    discount: 80,
    image: accessories_wallet1,
    colors: ["brown", "black"],
  },
  {
    id: 2,
    name: "Minimalist Card Holder",
    originalPrice: 29.99,
    salePrice: 5.99,
    discount: 80,
    image: accessories_wallet2,
    colors: ["navy", "grey"],
  },
  {
    id: 3,
    name: "Zip-Around Wallet",
    originalPrice: 59.99,
    salePrice: 11.99,
    discount: 80,
    image: accessories_wallet3,
    colors: ["red", "black"],
  },
  {
    id: 4,
    name: "Travel Wallet Organizer",
    originalPrice: 64.99,
    salePrice: 12.99,
    discount: 80,
    image: accessories_wallet4,
    colors: ["blue", "brown"],
  },
  {
    id: 5,
    name: "Classic Trifold Wallet",
    originalPrice: 39.99,
    salePrice: 7.99,
    discount: 80,
    image: accessories_wallet5,
    colors: ["black"],
  },
  {
    id: 6,
    name: "RFID Blocking Wallet",
    originalPrice: 54.99,
    salePrice: 10.99,
    discount: 80,
    image: accessories_wallet6,
    colors: ["grey", "green"],
  },
  {
    id: 7,
    name: "Coin Pocket Wallet",
    originalPrice: 34.99,
    salePrice: 6.99,
    discount: 80,
    image: accessories_wallet7,
    colors: ["brown"],
  },
  {
    id: 8,
    name: "Slim Business Wallet",
    originalPrice: 44.99,
    salePrice: 8.99,
    discount: 80,
    image: accessories_wallet8,
    colors: ["black", "tan"],
  },
];

const Accessories_wallet = () => {
  const navigate = useNavigate();
  const [favorites, setFavorites] = useState(new Set());
  const [openDropdown, setOpenDropdown] = useState(null);

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  const handleProductClick = (product) => {
    // Navigate to payment page with product information
    navigate("/payment", { state: { product } });
  };

  return (
    <div>
      <div className="homepage-container">
        <div className="header">
          <div className="logo-container">
            <Link to="/" className="logo-bg">
              <span className="logo-text">
                <span className="logo-star">★</span>
                StyleStore
              </span>
            </Link>
          </div>
          <div className="header-right">
            <div className="header-row">
              <input
                type="text"
                placeholder="Search"
                className="search-input"
              />
              <div className="icons-area">
                <Bell size={20} className="text-black" />
                <Heart size={20} className="text-black" />
                <div className="shopping-bag-rel">
                  <ShoppingBag size={22} className="text-black" />
                  <span className="shopping-bag-badge">0</span>
                </div>
                <Link to="/login" className="login-link">
                  LOGIN
                </Link>
                <Link to="/register" className="register-link">
                  REGISTER
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="category-nav">
          {categories.map((cat) => (
            <div
              key={cat.name}
              className={`category-group${
                openDropdown === cat.name ? " open" : ""
              }`}
              onMouseEnter={() => cat.items && setOpenDropdown(cat.name)}
              onMouseLeave={() => cat.items && setOpenDropdown(null)}
            >
              <button
                className={`category-btn${
                  cat.name === "Women" ? " women" : ""
                }`}
                style={{ fontWeight: 700 }}
                onClick={() =>
                  setOpenDropdown(cat.name === openDropdown ? null : cat.name)
                }
                type="button"
              >
                {cat.name}
              </button>
              {cat.items && (
                <div className="dropdown">
                  <ul>
                    {cat.items.map((item) => (
                      <li key={item.name}>
                        <Link to={item.link}>{item.name}</Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
        <div className="women-tshirt-container">
          <div className="sale-banner">
            <div className="banner-content">
              <div className="sale-text">
                <span className="up-to">UP TO</span>
                <span className="percentage">80% OFF</span>
                <span className="sale-subtitle">ON MID-YEAR SALE</span>
              </div>
            </div>
          </div>
          <div className="women-tshirt-title">Wallet</div>

          {/* Product Grid */}
          <div className="products-grids">
            {products.map((product) => (
              <div key={product.id} className="product-cards">
                <div className="product-image-containers">
                  <div className="discount-badge">{product.discount}%</div>
                  <button
                    className={`favorite-btn ${
                      favorites.has(product.id) ? "active" : ""
                    }`}
                    onClick={() => toggleFavorite(product.id)}
                  >
                    <Heart
                      size={16}
                      fill={favorites.has(product.id) ? "#ef4444" : "none"}
                    />
                  </button>
                  <img
                    src={product.image}
                    alt={product.name}
                    className="product-images"
                    onClick={() => handleProductClick(product)}
                    style={{ cursor: "pointer" }}
                  />
                </div>
                <div className="product-info">
                  <h3 className="product-name">{product.name}</h3>
                  <div className="price-container">
                    <span className="sale-price">US ${product.salePrice}</span>
                    <span className="original-price">
                      US ${product.originalPrice}
                    </span>
                  </div>
                  <div className="color-options">
                    {product.colors.map((color, index) => (
                      <div key={index} className={`color-dot ${color}`}></div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="Footer">
        <Footer />
      </div>
    </div>
  );
};

export default Accessories_wallet;
