/**
 * Database Connection Test Utility
 * Run this to test your database connection independently
 */

require('dotenv').config();
const { sequelize } = require('./config/database');

const testConnection = async () => {
  console.log('🧪 Testing database connection...');
  console.log('📋 Connection details:');
  console.log(`  Host: ${process.env.DB_HOST}`);
  console.log(`  Port: ${process.env.DB_PORT}`);
  console.log(`  Database: ${process.env.DB_NAME}`);
  console.log(`  User: ${process.env.DB_USER}`);
  
  try {
    // Test basic connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful!');
    
    // Test query execution
    const [results] = await sequelize.query('SELECT 1 + 1 AS result');
    console.log('✅ Query execution successful:', results);
    
    // Test connection pool
    const poolStats = sequelize.connectionManager.pool;
    console.log('📊 Connection pool stats:');
    console.log(`  Total connections: ${poolStats.size}`);
    console.log(`  Available connections: ${poolStats.available}`);
    console.log(`  Pending connections: ${poolStats.pending}`);
    
    // Close connection
    await sequelize.close();
    console.log('✅ Connection closed successfully');
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(`  Error: ${error.message}`);
    console.error(`  Code: ${error.code || 'N/A'}`);
    
    if (error.message.includes('ENOTFOUND')) {
      console.log('💡 Suggestion: Check your DB_HOST in .env file');
    } else if (error.message.includes('Access denied')) {
      console.log('💡 Suggestion: Check your DB_USER and DB_PASSWORD in .env file');
    } else if (error.message.includes('Unknown database')) {
      console.log('💡 Suggestion: Check your DB_NAME in .env file');
    }
    
    process.exit(1);
  }
};

// Run the test
testConnection();
