/* Enhanced Payment Modal CSS */
.payment-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(37, 99, 235, 0.1));
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: var(--space-5);
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.payment-container {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  max-width: 520px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--gray-200);
  animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-btn {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--gray-100);
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  z-index: 10;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.close-btn:hover {
  background-color: var(--gray-200);
  color: var(--text-primary);
  transform: rotate(90deg);
}

.payment-content {
  padding: var(--space-8) var(--space-6) var(--space-6);
}

.product-preview {
  text-align: center;
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.product-preview img {
  width: 220px;
  height: 220px;
  object-fit: cover;
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-lg);
  transition: transform var(--transition-slow);
}

.product-preview img:hover {
  transform: scale(1.02);
}

.product-preview h2 {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: 600;
  margin: var(--space-4) 0 var(--space-2) 0;
  color: var(--text-primary);
  line-height: 1.3;
}

.product-code {
  color: var(--text-muted);
  font-size: var(--text-sm);
  font-weight: 500;
  background: var(--gray-100);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  display: inline-block;
  margin: 0;
}

.product-details {
  width: 100%;
}

.selection-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.color-selection,
.size-selection {
  min-width: 0;
}

.color-selection strong,
.size-selection strong {
  display: block;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.color-options {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.color-btn {
  width: 36px;
  height: 36px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.color-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.color-btn.selected {
  border-color: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgb(37 99 235 / 0.2);
}

.color-btn.selected::after {
  content: '✓';
  position: absolute;
  color: white;
  font-weight: bold;
  font-size: var(--text-xs);
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.size-select {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  width: 100%;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.size-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
  outline: none;
}

.price-info {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: linear-gradient(135deg, var(--gray-50), var(--bg-primary));
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
}

.price-row {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.current-price {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--error-color);
  font-family: var(--font-family-display);
}

.original-price {
  text-decoration: line-through;
  color: var(--text-muted);
  font-size: var(--text-lg);
  font-weight: 500;
}

.discount {
  color: var(--error-color);
  font-weight: 600;
  font-size: var(--text-sm);
  background: rgba(239, 68, 68, 0.1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
}

.quantity-control strong {
  color: var(--text-primary);
  font-size: var(--text-base);
  font-weight: 600;
}

.quantity-buttons {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  background: var(--bg-primary);
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.quantity-buttons button {
  width: 44px;
  height: 44px;
  border: 2px solid var(--gray-300);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-primary);
}

.quantity-buttons button:hover:not(:disabled) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.quantity-buttons button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background-color: var(--gray-100);
}

.quantity-display {
  font-weight: 700;
  font-size: var(--text-xl);
  min-width: 40px;
  text-align: center;
  color: var(--text-primary);
  font-family: var(--font-family-display);
}

.summary {
  background: linear-gradient(135deg, var(--gray-50), var(--bg-primary));
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-6);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
  font-size: var(--text-base);
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-row.save {
  color: var(--success-color);
  font-weight: 600;
  background: rgba(16, 185, 129, 0.1);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  margin: var(--space-2) 0;
}

.summary-row.total {
  font-size: var(--text-lg);
  font-weight: 700;
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  color: var(--text-primary);
  font-family: var(--font-family-display);
}

.summary-divider {
  border: none;
  border-top: 2px solid var(--gray-200);
  margin: var(--space-4) 0;
  opacity: 0.7;
}

.checkout-btn {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.checkout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.checkout-btn:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.checkout-btn:hover::before {
  left: 100%;
}

.checkout-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-overlay {
    padding: var(--space-4);
  }

  .payment-container {
    max-height: 95vh;
    border-radius: var(--radius-xl);
  }

  .payment-content {
    padding: var(--space-6) var(--space-4) var(--space-4);
  }

  .product-preview img {
    width: 180px;
    height: 180px;
  }

  .product-preview h2 {
    font-size: var(--text-xl);
  }

  .selection-row {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .quantity-control {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .quantity-buttons {
    justify-content: center;
  }

  .summary {
    padding: var(--space-4);
  }

  .checkout-btn {
    padding: var(--space-4);
    font-size: var(--text-base);
  }
}

@media (max-width: 480px) {
  .payment-overlay {
    padding: var(--space-2);
  }

  .payment-content {
    padding: var(--space-4) var(--space-3) var(--space-3);
  }

  .product-preview img {
    width: 150px;
    height: 150px;
  }

  .product-preview h2 {
    font-size: var(--text-lg);
  }

  .price-info,
  .quantity-control,
  .summary {
    padding: var(--space-3);
  }
}

/* Loading States */
.payment-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

.payment-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  border: 3px solid var(--gray-300);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced Accessibility */
.payment-container:focus-within {
  box-shadow: var(--shadow-2xl), 0 0 0 4px rgb(37 99 235 / 0.1);
}

/* Print Styles */
@media print {
  .payment-overlay {
    display: none;
  }
}
