/* PaymentPage.css */

.payment-page {
  min-height: 100vh;
  width: 100%;
 
  font-family: "Inter", "Segoe UI", "Roboto", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, sans-serif;
}

/* Simple Header Styles */
.simple-header {
  background: white;
  border-bottom: 2px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-link {
  text-decoration: none;
}

.logo-text {
  font-size: 28px;
  font-weight: 800;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-star {
  color: #3b82f6;
  font-size: 32px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header-icons {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.icon {
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon:hover {
  color: #3b82f6;
  transform: translateY(-2px);
}

.shopping-bag-container {
  position: relative;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.auth-link {
  text-decoration: none;
  color: #4a5568;
  font-weight: 600;
  font-size: 14px;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.auth-link:hover {
  color: #3b82f6;
  background: #f1f5f9;
}

/* Legacy Header styles for backward compatibility */
.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-bg {
  text-decoration: none;
  color: #041b4b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  width: 300px;
}

.icons-area {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.shopping-bag-rel {
  position: relative;
}

.shopping-bag-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: #dc2626;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.login-link, .register-link {
  text-decoration: none;
  color: #374151;
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.login-link:hover, .register-link:hover {
  background-color: #f3f4f6;
}

/* Category Navigation */
.category-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.category-group {
  position: relative;
}

.category-btn {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 16px;
  cursor: pointer;
  color: #374151;
  text-decoration: none;
  transition: color 0.2s;
}

.category-btn:hover {
  color: #041b4b;
}

.category-btn.women {
  color: #041b4b;
  font-weight: bold;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 150px;
}

.dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdown li {
  padding: 0;
}

.dropdown a {
  display: block;
  padding: 0.5rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s;
}

.dropdown a:hover {
  background-color: #f3f4f6;
}

/* Back Navigation */
.back-navigation {
  padding: 1rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
 
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: #0369a1;
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Main Payment Container */
.payment-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 4rem;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 450px;
  gap: 4rem;
  align-items: start;
}

/* Payment Form Section */
.payment-form-section {
  background: white;
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  
}

.checkout-header h2 {
  font-size: 36px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s;
}

.back-btn:hover {
  color: #374151;
}

/* Main Payment Container */
.payment-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  align-items: start;
}

/* Payment Form Section */
.payment-form-section {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.payment-form-section h2 {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1.5rem;
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-size: 14px;
}

/* Form Styles */
.payment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-number {
  background: #3b82f6;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Payment Methods */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
}

.payment-option:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.payment-option.selected {
  border-color: #2563eb;
  background-color: #eff6ff;
}

.payment-option input[type="radio"] {
  margin: 0;
}

/* Card Details */
.card-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

/* Place Order Button */
.place-order-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.place-order-btn:hover:not(:disabled) {
  background: #1d4ed8;
}

.place-order-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Order Summary Section */
.order-summary-section {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 2rem;
}

.order-summary-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
}

/* Product Summary */
.product-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 0.5rem;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.sale-price {
  font-weight: 600;
  color: #dc2626;
  font-size: 18px;
}

.original-price {
  text-decoration: line-through;
  color: #6b7280;
  font-size: 14px;
}

/* Product Options */
.product-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.option-group select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 14px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.quantity-controls button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  width: 32px;
  height: 32px;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quantity-controls button:hover:not(:disabled) {
  background: #e5e7eb;
}

.quantity-controls button:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.quantity-controls span {
  min-width: 30px;
  text-align: center;
  font-weight: 500;
}

/* Price Breakdown */
.price-breakdown {
  margin-bottom: 1.5rem;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 14px;
}

.price-row.savings {
  color: #10b981;
}

.price-row.total {
  font-weight: 600;
  font-size: 18px;
  color: #111827;
  border-top: 1px solid #e5e7eb;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.price-breakdown hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 0.5rem 0;
}

/* Delivery Info */
.delivery-info {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.delivery-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
}

.delivery-info p {
  font-size: 14px;
  color: #6b7280;
  margin: 0.25rem 0;
}

/* Order Success */
.order-success {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.success-container {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.success-icon {
  margin-bottom: 1.5rem;
}

.success-container h1 {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

.success-container p {
  color: #6b7280;
  margin-bottom: 2rem;
}

.order-details {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.order-details h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.order-details p {
  margin: 0.5rem 0;
  color: #374151;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.continue-shopping-btn,
.view-orders-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
  display: inline-block;
}

.continue-shopping-btn {
  background: #2563eb;
  color: white;
  border: none;
}

.continue-shopping-btn:hover {
  background: #1d4ed8;
}

.view-orders-btn {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.view-orders-btn:hover {
  background: #f9fafb;
}

/* Loading */
.loading-container {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  font-size: 18px;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .order-summary-section {
    position: static;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .trust-indicators {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .category-nav {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
  }
  
  .payment-container {
    padding: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

/* Enhanced Order Summary Styles */
.order-summary-section.enhanced {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  min-width: 400px;
}

.order-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f3f4f6;
}

.order-summary-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.summary-badge {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.item-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Enhanced Product Summary */
.product-summary.enhanced {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.product-image-container {
  position: relative;
  margin-bottom: 1rem;
}

.product-image-large {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.sale-badge-summary {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.product-details-enhanced {
  text-align: center;
}

.product-name-large {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.product-price-display {
  margin-bottom: 1.5rem;
}

.current-price-large {
  font-size: 24px;
  font-weight: 700;
  color: #dc2626;
  margin-right: 0.75rem;
}

.original-price-crossed {
  font-size: 18px;
  color: #6b7280;
  text-decoration: line-through;
}

/* Enhanced Product Options */
.product-options-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.option-group-enhanced {
  text-align: left;
}

.option-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 14px;
}

/* Color Selector */
.color-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: none;
}

.color-option.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.color-option:hover {
  transform: scale(1.1);
}

.selected-option {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}

/* Size Selector */
.size-selector {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.size-option {
  padding: 0.5rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 44px;
  text-align: center;
}

.size-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.size-option:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

/* Enhanced Quantity Controls */
.quantity-section {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.quantity-controls-enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 0.75rem 0;
}

.quantity-btn {
  width: 44px;
  height: 44px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-weight: 600;
}

.quantity-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  background: #eff6ff;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-btn.increase {
  border-color: #10b981;
  color: #10b981;
}

.quantity-btn.increase:hover:not(:disabled) {
  background: #ecfdf5;
  border-color: #059669;
}

.quantity-btn.decrease {
  border-color: #ef4444;
  color: #ef4444;
}

.quantity-btn.decrease:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #dc2626;
}

.quantity-display {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  min-width: 60px;
  text-align: center;
}

.quantity-number {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.item-total-display {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 0.75rem;
  text-align: center;
  margin-top: 0.75rem;
}

.item-total-label {
  display: block;
  font-size: 14px;
  color: #0369a1;
  margin-bottom: 0.25rem;
}

.item-total-price {
  font-size: 18px;
  font-weight: 700;
  color: #0c4a6e;
}

/* Enhanced Price Breakdown */
.price-breakdown-enhanced {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.breakdown-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
  text-align: center;
}

.price-row-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.price-row-enhanced:last-child {
  border-bottom: none;
}

.price-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  color: #4b5563;
}

.price-value {
  font-weight: 600;
  color: #1f2937;
}

.price-row-enhanced.savings {
  background: #f0fdf4;
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border: 1px solid #bbf7d0;
}

.savings-label {
  color: #059669;
  font-weight: 600;
}

.savings-value {
  color: #059669;
  font-weight: 700;
}

.savings-icon, .delivery-icon {
  font-size: 1rem;
}

.price-divider {
  height: 2px;
  background: linear-gradient(90deg, #e5e7eb, #d1d5db, #e5e7eb);
  margin: 1rem 0;
  border-radius: 1px;
}

.total-row {
  background: #1f2937;
  color: white;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.total-label {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
}

.total-value {
  font-size: 1.5rem;
  font-weight: 800;
  color: #fbbf24;
}

.savings-summary {
  text-align: center;
  margin-top: 1rem;
}

.total-savings {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 1rem;
  font-weight: 600;
  margin: 0;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Enhanced Payment Methods */
.payment-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.payment-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0.5rem 0 1.5rem 0;
}

.payment-methods-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-option-enhanced {
  display: block;
  cursor: pointer;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 0;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.payment-option-enhanced:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.payment-option-enhanced.selected {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.payment-option-enhanced input[type="radio"] {
  display: none;
}

.payment-option-content-enhanced {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  gap: 1rem;
}

.payment-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.payment-option-enhanced.selected .payment-icon-container {
  background: #3b82f6;
  border-color: #3b82f6;
}

.payment-option-enhanced.selected .payment-method-icon {
  color: white;
}

.payment-icon-container.paypal {
  background: #0070ba;
  border-color: #0070ba;
}

.payment-icon-container.cod {
  background: #f59e0b;
  border-color: #f59e0b;
}

.payment-icon-emoji {
  font-size: 1.5rem;
}

.payment-method-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.payment-method-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.payment-method-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
}

.card-logos-enhanced {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.card-logo {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.visa-enhanced {
  background: #1a1f71;
  color: white;
}

.mastercard-enhanced {
  background: #eb001b;
  color: white;
}

.amex-enhanced {
  background: #006fcf;
  color: white;
}

.payment-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .order-summary-section.enhanced {
    min-width: unset;
    padding: 1.5rem;
  }

  .order-summary-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .product-image-large {
    height: 150px;
  }

  .quantity-controls-enhanced {
    gap: 0.75rem;
  }

  .quantity-btn {
    width: 40px;
    height: 40px;
  }

  .payment-option-content-enhanced {
    padding: 1rem;
    gap: 0.75rem;
  }

  .payment-icon-container {
    width: 40px;
    height: 40px;
  }
}

/* Enhanced Order Summary Styles */
.order-summary-section.enhanced {
  background: white;
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  height: fit-content;
  position: sticky;
  top: 120px;
}

.order-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.order-summary-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.summary-badge {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.product-summary.enhanced {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f1f5f9;
}

.product-image-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.product-image-large {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sale-badge-summary {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 700;
}

.product-details-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-name-large {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.product-price-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.current-price-large {
  font-size: 2rem;
  font-weight: 800;
  color: #059669;
}

.original-price-crossed {
  font-size: 1.25rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.product-options-enhanced {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.option-group-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-label {
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
}

.color-selector {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-option.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.color-option svg {
  color: #3b82f6;
}

.selected-option {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.size-selector {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.size-option {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
  color: #374151;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.size-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.size-option:hover:not(.selected) {
  border-color: #3b82f6;
  background: #f0f7ff;
}

.quantity-section {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
}

.quantity-controls-enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin: 1rem 0;
}

.quantity-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #e5e7eb;
  background: white;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.quantity-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  min-width: 80px;
  text-align: center;
}

.quantity-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
}

.item-total-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1rem;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
}

.item-total-label {
  font-weight: 600;
  color: #374151;
}

.item-total-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #059669;
}

.price-breakdown-enhanced {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  margin-bottom: 2rem;
}

.breakdown-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1.5rem;
  text-align: center;
}

.price-row-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem 0;
  font-size: 1rem;
}

.price-label {
  color: #4a5568;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-value {
  font-weight: 600;
  color: #1a202c;
}

.savings-label {
  color: #059669 !important;
  font-weight: 600;
}

.savings-value {
  color: #059669 !important;
  font-weight: 700;
}

.savings-icon, .delivery-icon {
  font-size: 18px;
}

.price-divider {
  height: 2px;
  background: #e2e8f0;
  margin: 1.5rem 0;
  border-radius: 1px;
}

.total-row {
  background: white;
  padding: 1rem;
  border-radius: 0.75rem;
  border: 2px solid #3b82f6;
  margin-bottom: 0;
}

.total-label {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
}

.total-value {
  font-size: 24px;
  font-weight: 800;
  color: #3b82f6;
}

.savings-summary {
  text-align: center;
  margin-top: 1rem;
}

.total-savings {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 1rem;
  font-weight: 600;
  margin: 0;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Two-Step Layout Styles */

/* Product Details Step (First Interface) */
.product-details-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  padding: 50px;
  border-radius: 12px;
  max-width: 1400px;
  margin: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
.product-details-content {
  width: 70%;
  height: 800px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
  background-color: rgb(245, 245, 248);
}

.product-large-image-container {
  position: relative;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.product-large-image {
  width: 100%;
  height: 500px;
  object-fit: cover;
}

.sale-badge-large {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: #ef4444;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.product-info-section {
  padding: 30px;
  

}

.product-title {
  font-size: 20px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 20px;
  line-height: 1.2;
  letter-spacing: -1px;
}

.product-price-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  border-radius: 1rem;
  
}

.current-price-large {
  font-size: 25px;
  font-weight: 800;
  color: #059669;
}

.original-price-large {
  
  font-size: 20px;
  color: #9ca3af;
  text-decoration: line-through;
}

.selection-group {

  margin-bottom: 20px;
  border-radius: 1rem;
  margin-top: 20px;
}

.selection-label {
  display: block;
  font-size: 20px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 20px;
}

.color-options {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.color-option-large {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.color-option-large.selected {
  border-color: #059669;
  box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.2);
  transform: scale(1.1);
}

.color-option-large:hover {
  transform: scale(1.05);
}

.selected-display {
  font-size: 18px;
  color: #6b7280;
  font-weight: 500;
  font-style: italic;
  margin-top: 20px;
}

.size-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.size-option-large {
  padding: 1rem 1.5rem;
  border: 2px solid #6685c2;
 
  border-radius: 0.75rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
  text-align: center;
  font-size: 16px;
}

.size-option-large.selected {
  border-color: #059669;
  
  color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
}

.size-option-large:hover {
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.quantity-selector-large {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 20px;
  border-radius: 1rem;
  border: 2px solid #9ebdfa;
}

.quantity-btn-large {
 
  width: 50px;
  height: 50px;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quantity-btn-large:hover:not(:disabled) {
  border-color: #059669;
  background: #f0fdf4;
  transform: scale(1.05);
}

.quantity-btn-large:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display-large {
  font-size: 24px;
  font-weight: 800;
  color: #1a202c;
  min-width: 80px;
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
}

.quantity-total {
  text-align: center;
  margin-top: 50px;
}

.quantity-total span {
  font-size: 20px;
  font-weight: 700;
  color: #059669;
  padding: 0.75rem 1.5rem;
  background: #f0fdf4;
  border-radius: 0.75rem;
  border: 1px solid #bbf7d0;
}

.proceed-checkout-btn {
  width: 80%;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  padding: 1.5rem 1.5rem;
  border-radius: 1rem;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.proceed-checkout-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(5, 150, 105, 0.4);
  background: linear-gradient(135deg, #047857, #065f46);
}

/* Checkout Step (Second Interface) */
.checkout-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;

}

.checkout-content {
  display: grid;
  grid-template-columns: 2fr 1.2fr;
  gap: 4rem;
  align-items: start;
}

/* Order Summary Container - Now Larger and More Dominant */
.order-summary-container {
  background: white;
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
  height: fit-content;
  position: sticky;
  top: 2rem;
  border: 1px solid #e5e7eb;
}

.order-summary-container h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.5px;
}

.summary-product {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
}

.summary-product-image {
  width: 180px;
  height: 200px;
  object-fit: cover;
  border-radius: 1rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.summary-product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summary-product-details h3 {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.summary-product-details p {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-product-details p strong {
  color: #374151;
  font-weight: 600;
}

.summary-price {
  font-weight: 800;
  color: #059669 !important;
  font-size: 20px;
}

.price-summary {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.price-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  font-size: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.price-line:last-child {
  border-bottom: none;
}

.price-line.savings {
  background: #f0fdf4;
  margin: 0.5rem -1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  color: #059669;
  font-weight: 600;
}

.price-line.total {
  background: #3d659e;
  color: white;
  margin: 1rem -1rem 0;
  padding: 1.5rem;
  border-radius: 0.75rem;
  font-size: 20px;
  font-weight: 700;
}

.price-divider {
  height: 2px;
  background: #e2e8f0;
  margin: 1.5rem 0;
  border-radius: 1px;
}

/* Payment Details Container - Streamlined */
.payment-details-container {
  background: white;
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.payment-details-container h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.5px;
}

.payment-methods-section h3,
.card-details-section h3,
.shipping-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1.5rem;
}

.payment-options {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.payment-option.selected {
  border-color: #059669;
  background: #f0fdf4;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.15);
}

.payment-option:hover {
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.payment-option input {
  margin-right: 1.5rem;
  width: 20px;
  height: 20px;
}

.payment-option-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.payment-icon {
  font-size: 24px;
  color: #374151;
}

.payment-option.selected .payment-icon {
  color: #059669;
}

.card-brands {
  margin-left: auto;
  display: flex;
  gap: 0.75rem;
}

.visa, .mastercard {
  font-size: 12px;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.visa {
  background: #1a365d;
  color: white;
}

.mastercard {
  background: #eb1c26;
  color: white;
}

.form-grid {
  display: grid;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  font-size: 16px;
}

.form-field input,
.form-field select {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafafa;
}

.form-field input:focus,
.form-field select:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  background: white;
}

.confirm-order-btn {
  width: 100%;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  padding: 1.5rem 2rem;
  border-radius: 1rem;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 2rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.confirm-order-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(5, 150, 105, 0.4);
  background: linear-gradient(135deg, #047857, #065f46);
}

.confirm-order-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.processing {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Back Navigation */
.back-navigation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  margin-top: 100px;
}

.back-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading {
  font-size: 1.25rem;
  color: #6b7280;
}

/* Order Success */
.order-success {
  max-width: 900px;
  margin: 0 auto;
  padding: 4rem 2rem;
  text-align: center;
}

.success-container {
  background: white;
  border-radius: 1.5rem;
  padding: 4rem;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
}

.success-icon {
  margin-bottom: 2rem;
}

.order-success h1 {
  font-size: 40px;
  font-weight: 800;
  color: #059669;
  margin-bottom: 1.5rem;
  letter-spacing: -0.5px;
}

.order-success p {
  font-size: 18px;
  color: #6b7280;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.order-details {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2.5rem;
  border: 1px solid #e2e8f0;
}

.order-details h3 {
  font-size: 22px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1.5rem;
}

.order-item {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  text-align: left;
}

.order-item-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-item-info {
  flex: 1;
}

.order-item-info p {
  margin-bottom: 0.5rem;
  font-size: 16px;
}

.order-total {
  font-size: 20px;
  color: #059669 !important;
  font-weight: 700;
}

.action-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
}

.continue-shopping-btn,
.view-orders-btn {
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.continue-shopping-btn {
  background: #f3f4f6;
  border: 2px solid #e5e7eb;
  color: #374151;
}

.continue-shopping-btn:hover {
  background: #e5e7eb;
  transform: translateY(-2px);
}

.view-orders-btn {
  background: linear-gradient(135deg, #059669, #047857);
  border: 2px solid #059669;
  color: white;
}

.view-orders-btn:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  border-color: #047857;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-details-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .checkout-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .order-summary-container {
    order: 2;
    position: static;
  }
  
  .payment-details-container {
    order: 1;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .product-title {
    font-size: 32px;
  }
  
  .current-price-large {
    font-size: 28px;
  }
  
  .original-price-large {
    font-size: 22px;
  }
  
  .summary-product {
    flex-direction: column;
    text-align: center;
  }
  
  .summary-product-image {
    width: 100%;
    height: 250px;
  }
  
  .order-summary-container h2,
  .payment-details-container h2 {
    font-size: 24px;
  }
  
  .summary-product-details h3 {
    font-size: 20px;
  }
  
  .checkout-container {
    padding: 1rem;
  }
  
  .order-summary-container,
  .payment-details-container {
    padding: 2rem;
  }
}
